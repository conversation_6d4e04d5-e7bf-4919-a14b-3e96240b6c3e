<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>本地视频播放器 - 支持MKV/MP4格式</title>
  <!-- Plyr CSS from CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/plyr@3.7.8/dist/plyr.css">
  <style>
    :root {
      --primary-color: #4285f4;
      --error-color: #ea4335;
      --success-color: #34a853;
      --text-color: #333;
      --light-gray: #f5f5f5;
      --dark-gray: #757575;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--light-gray);
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      text-align: center;
      margin-bottom: 20px;
      color: var(--primary-color);
    }
    
    .description {
      text-align: center;
      margin-bottom: 30px;
      color: var(--dark-gray);
    }
    
    .file-input-container {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-bottom: 25px;
    }
    
    .file-upload-wrapper {
      position: relative;
      overflow: hidden;
      display: inline-block;
    }
    
    .file-upload-button {
      padding: 12px 20px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .file-upload-button:hover {
      background-color: #3367d6;
    }
    
    .file-upload-input {
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
    
    .file-name {
      margin-left: 15px;
      font-size: 14px;
      color: var(--dark-gray);
    }
    
    .controls {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-bottom: 25px;
    }
    
    .btn {
      padding: 12px 24px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .btn:hover {
      background-color: #3367d6;
      transform: translateY(-2px);
    }
    
    .btn:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
      transform: none;
    }
    
    .video-container {
      width: 100%;
      margin-top: 20px;
      border-radius: 8px;
      overflow: hidden;
      background-color: black;
    }
    
    #player {
      width: 100%;
      max-height: 80vh;
    }
    
    .status {
      text-align: center;
      margin: 15px 0;
      min-height: 24px;
    }
    
    .error {
      color: var(--error-color);
      font-weight: bold;
    }
    
    .success {
      color: var(--success-color);
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(0,0,0,.1);
      border-radius: 50%;
      border-top-color: var(--primary-color);
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
      vertical-align: middle;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .hidden {
      display: none;
    }
    
    @media (max-width: 768px) {
      .container {
        padding: 15px;
      }
      
      .controls {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>本地视频播放器</h1>
    <p class="description">支持 MKV/MP4 视频格式和 ASS/SRT 字幕格式</p>
    
    <div class="file-input-container">
      <div>
        <div class="file-upload-wrapper">
          <button class="file-upload-button">选择视频文件 (.mkv/.mp4)</button>
          <input type="file" id="videoInput" class="file-upload-input" accept=".mkv,.mp4">
        </div>
        <span id="videoFileName" class="file-name">未选择文件</span>
      </div>
      
      <div>
        <div class="file-upload-wrapper">
          <button class="file-upload-button">选择字幕文件 (.ass/.srt)</button>
          <input type="file" id="subtitleInput" class="file-upload-input" accept=".ass,.srt">
        </div>
        <span id="subtitleFileName" class="file-name">未选择文件 (可选)</span>
      </div>
    </div>
    
    <div class="controls">
      <button id="playBtn" class="btn" disabled>播放</button>
      <button id="resetBtn" class="btn">重置</button>
    </div>
    
    <div id="status" class="status"></div>
    
    <div class="video-container">
      <video id="player" controls>
        <track kind="subtitles" id="subtitleTrack" srclang="zh" label="中文字幕">
      </video>
    </div>
  </div>

  <!-- Plyr JS from CDN -->
  <script src="https://cdn.jsdelivr.net/npm/plyr@3.7.8/dist/plyr.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化 Plyr 播放器
      const player = new Plyr('#player', {
        controls: [
          'play-large',
          'play',
          'progress',
          'current-time',
          'mute',
          'volume',
          'captions',
          'settings',
          'fullscreen'
        ],
        captions: { active: true, language: 'zh', update: true },
        settings: ['captions', 'quality', 'speed', 'loop']
      });
      
      // 获取 DOM 元素
      const videoInput = document.getElementById('videoInput');
      const subtitleInput = document.getElementById('subtitleInput');
      const videoFileName = document.getElementById('videoFileName');
      const subtitleFileName = document.getElementById('subtitleFileName');
      const playBtn = document.getElementById('playBtn');
      const resetBtn = document.getElementById('resetBtn');
      const statusDiv = document.getElementById('status');
      const subtitleTrack = document.getElementById('subtitleTrack');
      const videoElement = document.getElementById('player');
      
      // 文件选择事件处理
      videoInput.addEventListener('change', function(e) {
        if (this.files.length) {
          const file = this.files[0];
          if (!file.name.match(/\.(mkv|mp4)$/i)) {
            showStatus('错误：仅支持 .mkv 或 .mp4 视频文件！', 'error');
            resetFileInput(this, videoFileName);
            return;
          }
          videoFileName.textContent = file.name;
          playBtn.disabled = false;
        }
      });
      
      subtitleInput.addEventListener('change', function(e) {
        if (this.files.length) {
          const file = this.files[0];
          if (!file.name.match(/\.(ass|srt)$/i)) {
            showStatus('错误：仅支持 .ass 或 .srt 字幕文件！', 'error');
            resetFileInput(this, subtitleFileName);
            return;
          }
          subtitleFileName.textContent = file.name;
        }
      });
      
      // 播放按钮点击事件
      playBtn.addEventListener('click', function() {
        loadMedia();
      });
      
      // 重置按钮点击事件
      resetBtn.addEventListener('click', function() {
        resetPlayer();
      });
      
      // 加载媒体文件
      function loadMedia() {
        // 验证视频文件
        if (!videoInput.files.length) {
          showStatus('请先选择视频文件！', 'error');
          return;
        }
        
        const videoFile = videoInput.files[0];
        const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
        
        if (videoFile.size > maxSize) {
          showStatus('错误：视频文件太大，建议使用小于2GB的文件！', 'error');
          return;
        }
        
        showStatus('正在加载媒体文件... <span class="loading"></span>', '');
        
        // 创建视频URL
        const videoURL = URL.createObjectURL(videoFile);
        videoElement.src = videoURL;
        
        // 处理字幕文件
        if (subtitleInput.files.length) {
          const subtitleFile = subtitleInput.files[0];
          const subtitleURL = URL.createObjectURL(subtitleFile);
          subtitleTrack.src = subtitleURL;
          videoElement.textTracks[0].mode = 'showing';
        } else {
          subtitleTrack.src = '';
        }
        
        // 视频加载完成事件
        videoElement.onloadeddata = function() {
          showStatus('媒体加载完成！', 'success');
          videoElement.play().catch(error => {
            showStatus(`播放失败：${error.message}`, 'error');
          });
        };
        
        // 视频错误事件
        videoElement.onerror = function() {
          showStatus('视频播放出错，请检查文件格式！', 'error');
        };
      }
      
      // 重置播放器
      function resetPlayer() {
        // 释放之前的URL对象
        if (videoElement.src) {
          URL.revokeObjectURL(videoElement.src);
        }
        if (subtitleTrack.src) {
          URL.revokeObjectURL(subtitleTrack.src);
        }
        
        // 重置UI
        videoElement.src = '';
        subtitleTrack.src = '';
        videoFileName.textContent = '未选择文件';
        subtitleFileName.textContent = '未选择文件 (可选)';
        resetFileInput(videoInput);
        resetFileInput(subtitleInput);
        playBtn.disabled = true;
        statusDiv.textContent = '';
        
        // 重置文件输入
        function resetFileInput(input, fileNameElement = null) {
          input.value = '';
          if (fileNameElement) {
            fileNameElement.textContent = input.id === 'subtitleInput' 
              ? '未选择文件 (可选)' 
              : '未选择文件';
          }
        }
      }
      
      // 显示状态信息
      function showStatus(message, type) {
        statusDiv.innerHTML = message;
        statusDiv.className = 'status';
        
        if (type === 'error') {
          statusDiv.classList.add('error');
        } else if (type === 'success') {
          statusDiv.classList.add('success');
        }
      }
      
      // 清理URL对象
      window.addEventListener('beforeunload', function() {
        if (videoElement.src) {
          URL.revokeObjectURL(videoElement.src);
        }
        if (subtitleTrack.src) {
          URL.revokeObjectURL(subtitleTrack.src);
        }
      });
    });
  </script>
</body>
</html>